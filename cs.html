<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CS内战匹配</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #fff;
            min-height: 100vh;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .player-pool, .team {
            background: rgba(42, 42, 42, 0.9);
            border-radius: 12px;
            padding: 20px;
            min-height: 350px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .player-pool {
            flex: 1;
        }
        
        .teams {
            flex: 2;
            display: flex;
            gap: 20px;
        }
        
        .team {
            flex: 1;
            border: 3px solid;
            position: relative;
            overflow: hidden;
        }
        
        .team.team-a {
            border-color: #4CAF50;
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(42, 42, 42, 0.9));
        }
        
        .team.team-b {
            border-color: #f44336;
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(42, 42, 42, 0.9));
        }
        
        .team::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, transparent, currentColor, transparent);
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .player {
            background: linear-gradient(135deg, #3a3a3a, #4a4a4a);
            margin: 8px 0;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            position: relative;
        }
        
        .player:hover {
            background: linear-gradient(135deg, #4a4a4a, #5a5a5a);
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        .player.selected {
            background: linear-gradient(135deg, #555, #666);
            border-left-color: #4CAF50;
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
        }
        
        .captain {
            background: linear-gradient(135deg, #ff9800, #ff5722) !important;
            font-weight: bold;
            border-left-color: #ffd700 !important;
            position: relative;
        }
        
        .captain::after {
            content: '👑';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .random-btn {
            background: linear-gradient(135deg, #ff9800, #e68900);
        }
        
        .clear-btn {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }
        
        h2 {
            text-align: center;
            margin-top: 0;
            font-size: 1.5em;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .team-count {
            font-size: 14px;
            color: #ccc;
            font-weight: normal;
        }
        
        .team-a h2 {
            color: #4CAF50;
        }
        
        .team-b h2 {
            color: #f44336;
        }
    </style>
</head>
<body>
    <h1>🎮 CS内战匹配系统 🎮</h1>
    
    <div class="container">
        <div class="player-pool">
            <h2>玩家池</h2>
            <div id="playerPool"></div>
        </div>
        
        <div class="teams">
            <div class="team team-a" id="teamA">
                <h2>A队 (磊磊队) <span class="team-count" id="aCount">(0/5)</span></h2>
                <div id="aPlayers"></div>
            </div>
            
            <div class="team team-b" id="teamB">
                <h2>B队 (洲洲队) <span class="team-count" id="bCount">(0/5)</span></h2>
                <div id="bPlayers"></div>
            </div>
        </div>
    </div>
    
    <div class="buttons">
        <button onclick="randomMatch()" class="random-btn">🎲 随机分队</button>
        <button onclick="clearTeams()" class="clear-btn">🗑️ 清空队伍</button>
        <button onclick="startMatch()">🚀 开始比赛</button>
    </div>

    <script>
        const players = ['磊磊', '洲洲', '肉哥', '大锤', '花姐', '大螂子', '金元宝', '元哥', '米娅', '天哥'];
        const captains = ['磊磊', '洲洲'];
        let selectedPlayer = null;

        function initPlayers() {
            const pool = document.getElementById('playerPool');
            pool.innerHTML = '';
            
            // 先添加队长到对应队伍
            addPlayerToTeam('a', '磊磊', true);
            addPlayerToTeam('b', '洲洲', true);
            
            // 添加其他玩家到玩家池
            players.filter(p => !captains.includes(p)).forEach(player => {
                const div = document.createElement('div');
                div.className = 'player';
                div.textContent = player;
                div.onclick = () => selectPlayer(div, player);
                pool.appendChild(div);
            });
            updateCounts();
        }

        function selectPlayer(element, player) {
            if (selectedPlayer) {
                selectedPlayer.classList.remove('selected');
            }
            selectedPlayer = element;
            element.classList.add('selected');
        }

        function addToTeam(team) {
            if (!selectedPlayer) return;
            
            const teamDiv = document.getElementById(team + 'Players');
            const currentCount = teamDiv.children.length;
            
            if (currentCount >= 5) {
                alert('队伍已满！');
                return;
            }
            
            const playerName = selectedPlayer.textContent;
            addPlayerToTeam(team, playerName);
            selectedPlayer.remove();
            selectedPlayer = null;
            updateCounts();
        }

        function addPlayerToTeam(team, playerName, isCaptain = false) {
            const teamDiv = document.getElementById(team + 'Players');
            const playerDiv = document.createElement('div');
            playerDiv.className = isCaptain ? 'player captain' : 'player';
            playerDiv.textContent = playerName;
            if (!isCaptain) {
                playerDiv.onclick = () => removeFromTeam(playerDiv, team);
            }
            teamDiv.appendChild(playerDiv);
        }

        function removeFromTeam(element, team) {
            const playerName = element.textContent;
            
            // 不允许移除队长
            if (captains.includes(playerName)) {
                alert('队长不能被移除！');
                return;
            }
            
            element.remove();
            
            const pool = document.getElementById('playerPool');
            const playerDiv = document.createElement('div');
            playerDiv.className = 'player';
            playerDiv.textContent = playerName;
            playerDiv.onclick = () => selectPlayer(playerDiv, playerName);
            pool.appendChild(playerDiv);
            updateCounts();
        }

        function updateCounts() {
            const aCount = document.getElementById('aPlayers').children.length;
            const bCount = document.getElementById('bPlayers').children.length;
            document.getElementById('aCount').textContent = `(${aCount}/5)`;
            document.getElementById('bCount').textContent = `(${bCount}/5)`;
        }

        function randomMatch() {
            clearTeams();
            const availablePlayers = players.filter(p => !captains.includes(p));

            // 定义不能在同一队的人员对
            const separatePairs = [
                ['肉哥', '元哥'],
                ['大锤', '金元宝'],
                ['花姐', '米娅']
            ];

            // 重新添加队长
            addPlayerToTeam('a', '磊磊', true);
            addPlayerToTeam('b', '洲洲', true);

            // 使用更智能的分配策略
            let validAssignment = false;
            let attempts = 0;
            const maxAttempts = 1000;

            while (!validAssignment && attempts < maxAttempts) {
                attempts++;

                // 清空队伍（保留队长）
                document.getElementById('aPlayers').innerHTML = '';
                document.getElementById('bPlayers').innerHTML = '';
                addPlayerToTeam('a', '磊磊', true);
                addPlayerToTeam('b', '洲洲', true);

                const teamA = ['磊磊'];
                const teamB = ['洲洲'];
                const remaining = [...availablePlayers];

                // 先处理有约束的玩家对
                const constrainedPlayers = new Set();
                separatePairs.forEach(pair => {
                    pair.forEach(player => constrainedPlayers.add(player));
                });

                // 随机分配有约束的玩家
                const shuffledConstrained = [...constrainedPlayers].sort(() => Math.random() - 0.5);

                for (const player of shuffledConstrained) {
                    if (!remaining.includes(player)) continue;

                    // 找到这个玩家的约束对象
                    const constraintPartner = separatePairs.find(pair => pair.includes(player))
                        ?.find(p => p !== player);

                    // 检查哪个队可以加入
                    const partnerInA = constraintPartner && teamA.includes(constraintPartner);
                    const partnerInB = constraintPartner && teamB.includes(constraintPartner);

                    if (partnerInA && teamB.length < 5) {
                        // 约束对象在A队，这个玩家必须去B队
                        teamB.push(player);
                        remaining.splice(remaining.indexOf(player), 1);
                    } else if (partnerInB && teamA.length < 5) {
                        // 约束对象在B队，这个玩家必须去A队
                        teamA.push(player);
                        remaining.splice(remaining.indexOf(player), 1);
                    } else if (!partnerInA && !partnerInB) {
                        // 约束对象还没分配，随机选择一队
                        if (teamA.length < 5 && teamB.length < 5) {
                            if (Math.random() < 0.5 && teamA.length < 5) {
                                teamA.push(player);
                            } else if (teamB.length < 5) {
                                teamB.push(player);
                            } else if (teamA.length < 5) {
                                teamA.push(player);
                            }
                            remaining.splice(remaining.indexOf(player), 1);
                        }
                    }
                }

                // 随机分配剩余的无约束玩家
                const shuffledRemaining = remaining.sort(() => Math.random() - 0.5);
                for (const player of shuffledRemaining) {
                    if (teamA.length < 5 && teamB.length < 5) {
                        if (Math.random() < 0.5) {
                            teamA.push(player);
                        } else {
                            teamB.push(player);
                        }
                    } else if (teamA.length < 5) {
                        teamA.push(player);
                    } else if (teamB.length < 5) {
                        teamB.push(player);
                    }
                }

                // 检查是否满足所有条件
                if (teamA.length === 5 && teamB.length === 5) {
                    // 验证约束条件
                    let constraintsValid = true;
                    for (const pair of separatePairs) {
                        const bothInA = pair.every(p => teamA.includes(p));
                        const bothInB = pair.every(p => teamB.includes(p));
                        if (bothInA || bothInB) {
                            constraintsValid = false;
                            break;
                        }
                    }

                    if (constraintsValid) {
                        validAssignment = true;
                        // 将分配结果显示到界面
                        teamA.slice(1).forEach(player => addPlayerToTeam('a', player));
                        teamB.slice(1).forEach(player => addPlayerToTeam('b', player));
                    }
                }
            }

            if (!validAssignment) {
                // 如果仍然无法找到有效分配，使用备用策略
                console.log('使用备用分配策略');
                document.getElementById('aPlayers').innerHTML = '';
                document.getElementById('bPlayers').innerHTML = '';
                addPlayerToTeam('a', '磊磊', true);
                addPlayerToTeam('b', '洲洲', true);

                // 手动分配确保约束满足
                const teamA = ['磊磊'];
                const teamB = ['洲洲'];

                // 分配约束对
                teamA.push('肉哥', '大锤', '花姐');
                teamB.push('元哥', '金元宝', '米娅');

                // 分配剩余玩家
                const remaining = availablePlayers.filter(p =>
                    !['肉哥', '元哥', '大锤', '金元宝', '花姐', '米娅'].includes(p)
                );

                if (remaining.length >= 1) teamA.push(remaining[0]);
                if (remaining.length >= 2) teamB.push(remaining[1]);

                // 显示结果
                teamA.slice(1).forEach(player => addPlayerToTeam('a', player));
                teamB.slice(1).forEach(player => addPlayerToTeam('b', player));
            }

            updateCounts();
        }

        function clearTeams() {
            document.getElementById('aPlayers').innerHTML = '';
            document.getElementById('bPlayers').innerHTML = '';
            initPlayers();
        }

        function startMatch() {
            const aCount = document.getElementById('aPlayers').children.length;
            const bCount = document.getElementById('bPlayers').children.length;
            
            if (aCount !== 5 || bCount !== 5) {
                alert('每队必须有5名玩家才能开始比赛！');
                return;
            }
            
            alert('🎉 比赛开始！祝各位玩得愉快！Good luck & have fun! 🎉');
        }

        // 添加点击队伍区域添加玩家的功能
        document.getElementById('teamA').onclick = (e) => {
            if (e.target.id === 'teamA' || e.target.id === 'aPlayers') {
                addToTeam('a');
            }
        };

        document.getElementById('teamB').onclick = (e) => {
            if (e.target.id === 'teamB' || e.target.id === 'bPlayers') {
                addToTeam('b');
            }
        };

        // 初始化
        initPlayers();
    </script>
</body>
</html>